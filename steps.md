Por favor, ayúdame a implementar los pasos faltantes para completar el Planificador de Mediano Plazo según la consigna. Necesito que:

1. Generes una checklist detallado con todas las tareas necesarias para completar la implementación (en el archivo steps.md)
2. Para cada tarea, especifica:
   - El archivo que debe modificarse
   - La función o sección específica que requiere cambios
   - El código que debe agregarse o modificarse
3. A medida que vayas completando cada tarea, márcala con una ✅ en la checklist
4. Después de cada tarea completada, proporciona una breve descripción de lo que se implementó y cómo funciona
5. Asegúrate de cubrir todos los requisitos faltantes:
   - Transición de SUSP_BLOCKED a SUSP_READY cuando finaliza IO
   - Verificación de memoria disponible después de mover a swap
   - Priorización de SUSP_READY sobre NEW en el planificador de largo plazo

<PERSON> favor, presenta el código en bloques que pueda aplicar directamente a mi proyecto.



Análisis de lo implementado vs. lo requerido
✅ Iniciar timer al entrar a BLOCKED: Se implementa en SyscallIO() cuando llama a IniciarTimerSuspension(pid).
✅ Transición a SUSP_BLOCKED: El planificador verifica si el timer expiró y mueve el proceso de ColaBlocked a ColaSuspBlocked.
✅ Informar a memoria para mover a swap: Se envía mensaje a memoria con mover-a-swap.
❌ Verificar si nuevos procesos pueden entrar: No se implementa la verificación para permitir que procesos de NEW o SUSP_READY entren cuando hay más memoria disponible.
❌ Transición de SUSP_BLOCKED a SUSP_READY al finalizar IO: No hay código que maneje esta transición cuando IO finaliza.
❌ Prioridad de SUSP_READY sobre NEW: No hay implementación del algoritmo que priorice SUSP_READY sobre NEW.
Conclusión
El código implementa parcialmente el Planificador de Mediano Plazo, pero faltan elementos importantes:

No hay manejo de la transición de SUSP_BLOCKED a SUSP_READY cuando finaliza IO.
No hay verificación para permitir nuevos procesos cuando se libera memoria.
No hay implementación de la prioridad de SUSP_READY sobre NEW.
Para completar la implementación según la consigna, se necesitaría:

Modificar el código que maneja la finalización de IO para mover procesos de SUSP_BLOCKED a SUSP_READY.
Implementar la verificación de memoria disponible después de mover a swap.
Modificar el planificador de largo plazo para priorizar SUSP_READY sobre NEW.